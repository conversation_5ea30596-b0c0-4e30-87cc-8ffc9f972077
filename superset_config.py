# Custom security manager to handle both AUTH_DB and AUTH_OAUTH
import os
import logging
import json
import site
from flask import (
    redirect as flask_redirect,
    request,
    url_for,
    flash,
    render_template_string,
    send_from_directory,
    session,
    g,
)
from flask_appbuilder.security.views import AuthDBView, expose
from flask_appbuilder.security.forms import LoginForm_db
from flask_appbuilder.security.decorators import no_cache
from flask_appbuilder.security.manager import AUTH_DB, AUTH_OAUTH
from flask_appbuilder.security.registerviews import RegisterUserDBView
from flask_appbuilder.utils.base import get_safe_redirect
from flask_appbuilder.security.utils import generate_random_string
from flask_login import login_user
from superset.security import SupersetSecurityManager
from jinja2 import ChoiceLoader, FileSystemLoader
import flask_appbuilder as fab
from typing import Dict, List, Optional, Type


# Setup template directory
APP_DIR = os.path.dirname(os.path.abspath(__file__))
TEMPLATES_DIR = os.path.join(APP_DIR, "templates")
os.makedirs(TEMPLATES_DIR, exist_ok=True)

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


# ==================================================
# CRITICAL CONFIG SETTINGS - DON'T CHANGE THESE
# ==================================================
AUTH_TYPE = AUTH_OAUTH  # Using OAuth authentication
AUTH_USER_REGISTRATION = True
AUTH_USER_REGISTRATION_ROLE = "Alpha"
AUTH_OAUTH_SHOW_DB_LOGIN_FORM = True

# OAuth Config Settings
OAUTH_PROVIDERS = [
    {
        "name": "google",
        "icon": "fa-google",
        "token_key": "access_token",
        "remote_app": {
            "client_id": "************-71mt67sc4kpn7vtmt2el83843b5nomqb.apps.googleusercontent.com",
            "client_secret": "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",
            "api_base_url": "https://www.googleapis.com/oauth2/v2/",
            "client_kwargs": {"scope": "email profile"},
            "request_token_url": None,
            "access_token_url": "https://accounts.google.com/o/oauth2/token",
            "authorize_url": "https://accounts.google.com/o/oauth2/auth",
        },
    }
]


# ==================================================
# CUSTOM LOGIN VIEW
# ==================================================
class CustomLoginView(AuthDBView):
    """Custom view that always shows DB login form alongside OAuth providers"""

    login_template = "custom_login.html"

    def render_template(self, template, **kwargs):
        """Override render_template to use our custom login template"""
        # Only override for the login template
        if template.endswith("login_oauth.html"):
            # Set base_template explicitly to prevent inheritance issues
            # kwargs["base_template"] = "appbuilder/base.html"

            # Try to use our custom template
            return super().render_template(self.login_template, **kwargs)

        return super().render_template(template, **kwargs)

    @expose("/login/", methods=["GET", "POST"])
    @expose("/login/<provider>")
    @no_cache
    def login(self, provider=None):
        if provider:
            try:
                if hasattr(AuthDBView, "parent") and hasattr(
                    AuthDBView.parent, "login"
                ):
                    logger.info("Login with provider: %s", provider)
                    return super(AuthDBView, self).login(provider)
                else:
                    redirect_url = url_for(f".oauth_authorize_{provider}")
                    logger.info("Login with provider: %s", redirect_url)
                    return flask_redirect(redirect_url)
            except Exception as e:
                logging.error(f"Error in OAuth login: {e}")
                # Fall back to showing the login form
                form = LoginForm_db()
                return self.render_template(
                    self.login_template,
                    title=self.title,
                    form=form,
                    appbuilder=self.appbuilder,
                    error=str(e),
                )

        # If user is already authenticated, redirect to index
        if g.user is not None and g.user.is_authenticated:
            return flask_redirect(self.appbuilder.get_url_for_index)

        # For GET requests show the login form
        if request.method == "GET":
            # Show our custom login template with the form
            form = LoginForm_db()
            return self.render_template(
                self.login_template,
                title=self.title,
                form=form,
                appbuilder=self.appbuilder,
            )

        # For POST requests (form submission)
        form = LoginForm_db()
        if form.validate_on_submit():
            next_url = get_safe_redirect(request.args.get("next", ""))
            user = self.appbuilder.sm.auth_user_db(
                form.username.data, form.password.data
            )
            if user:
                remember = getattr(form, "remember_me", None)
                remember_value = remember.data if remember else False

                # Just do standard login
                login_user(user, remember=remember_value)

                # Don't try to manipulate permissions in the session at all
                return flask_redirect(self.appbuilder.get_url_for_index)

            flash("Invalid login. Please try again.", "warning")
            return flask_redirect(self.appbuilder.get_url_for_login_with(next_url))

        # Form validation failed
        return self.render_template(
            self.login_template,
            title=self.title,
            form=form,
            appbuilder=self.appbuilder,
        )


# ==================================================
# CUSTOM SECURITY MANAGER
# ==================================================
class CustomSecurityManager(SupersetSecurityManager):
    """Custom security manager that uses our custom login view"""

    def __init__(self, appbuilder):
        # Override the authdbview BEFORE calling super
        self.authdbview = CustomLoginView
        super(CustomSecurityManager, self).__init__(appbuilder)

    def registerView(self):
        """Register our custom login view"""
        try:
            # Create an instance of the authdbview class
            login_view = self.authdbview()
            self.appbuilder.add_view_no_menu(login_view)
        except Exception as e:
            logger.error(f"Failed to register view: {str(e)}")

    # Override this critical permission method to allow admin access
    def has_access(self, permission_name, view_name):
        """
        Override permission checking to ensure admins can access all views
        """
        user = g.user

        # Always log permission checks to aid debugging
        logging.debug(
            f"Checking permission: {permission_name} on {view_name} for user {user.username}"
        )

        # Check if user has Admin role - if so, grant all permissions
        if hasattr(user, "roles"):
            admin_role_names = {"Admin", "admin"}
            is_admin = any(role.name in admin_role_names for role in user.roles)
            if is_admin:
                logging.debug(
                    f"User {user.username} is admin, granting access to {view_name}"
                )
                return True

        # Fall back to standard permission checking
        return super().has_access(permission_name, view_name)

    # Override menu building to ensure admin items show up
    def get_user_menu_access(self, menu_names):
        """
        Override menu access to ensure admins can see all menus
        """
        user = g.user

        # For admin users, grant access to all menus
        if hasattr(user, "roles"):
            admin_role_names = {"Admin", "admin"}
            is_admin = any(role.name in admin_role_names for role in user.roles)
            if is_admin:
                # Return True for all menus
                return {name: True for name in menu_names}

        # Fall back to standard access checks
        return super().get_user_menu_access(menu_names)


# Register our custom security manager class
SECURITY_MANAGER_CLASS = CustomSecurityManager
CUSTOM_STATIC_FOLDER = "/Users/<USER>/Sandbox/Mason/ajna-frontend/custom_static/"
# CUSTOM_STATIC_FOLDER = "/Users/<USER>/kubric-codebase/ajna-frontend/custom_static/"


def custom_static_setup(app):
    """Setup custom static files handling"""

    @app.route("/custom_static/<path:filename>")
    def custom_static(filename):
        return send_from_directory(CUSTOM_STATIC_FOLDER, filename)


# ==================================================
# FLASK APP MUTATOR
# ==================================================
def init_app(app):
    """Initialize the Flask app with our custom configurations"""
    # IMPORTANT: Do NOT set base_template to anything that would create a circular reference
    # The template can't extend itself through any chain of inheritance
    # Use a completely standalone base template that doesn't extend anything
    # app.jinja_env.globals["base_template"] = None

    # Add missing baselib with get_nonce method to the template context
    # class BaseLib:
    #     @staticmethod
    #     def get_nonce():
    #         return "dummy-nonce-for-csp"

    # app.jinja_env.globals["baselib"] = BaseLib()

    # Ensure the template folder is correctly set
    app.config["APP_TEMPLATE_FOLDER"] = TEMPLATES_DIR
    app.config["TEMPLATE_FOLDER"] = TEMPLATES_DIR
    app.config["FAB_TEMPLATE_FOLDER"] = TEMPLATES_DIR
    app.template_folder = TEMPLATES_DIR
    custom_static_setup(app)

    # Set up the template loader to prioritize our custom templates
    templates_loader = FileSystemLoader(TEMPLATES_DIR)

    # Add Superset's templates directory directly using site-packages
    site_packages = site.getsitepackages()[0]
    superset_templates_dir = os.path.join(site_packages, "superset", "templates")
    superset_loader = FileSystemLoader(superset_templates_dir)

    # Create loader that prioritizes our templates but includes Superset's templates
    app.jinja_env.loader = ChoiceLoader(
        [
            templates_loader,  # First check our custom templates
            superset_loader,  # Then check Superset's templates directory
            app.jinja_env.loader,  # Then use the original loader as fallback
        ]
    )

    # Get the security manager instance
    if hasattr(app, "appbuilder") and hasattr(app.appbuilder, "sm"):
        sm = app.appbuilder.sm

        # Check if it's our custom security manager
        if isinstance(sm, CustomSecurityManager):
            sm.registerView()
        else:
            try:
                # Try to recreate app builder with our security manager
                app.appbuilder._security_manager = CustomSecurityManager(app.appbuilder)
                app.appbuilder._security_manager.registerView()
            except Exception as e:
                logger.error(f"Failed to replace security manager: {str(e)}")

    # Monkey patch the render_template method of AuthDBView to handle base template issues
    original_render = AuthDBView.render_template

    def patched_render_template(self, template, **kwargs):
        # If it's the login template, ensure we handle it properly
        if template.endswith("login_oauth.html"):
            # Set base_template explicitly
            kwargs["base_template"] = "appbuilder/base.html"

            try:
                # Try to read our custom template directly
                custom_template_path = os.path.join(TEMPLATES_DIR, "custom_login.html")

                if os.path.exists(custom_template_path):
                    with open(custom_template_path, "r") as f:
                        template_content = f.read()

                    # Directly render the template string with our context
                    return render_template_string(template_content, **kwargs)
            except Exception as e:
                logger.error(f"Error reading custom template: {str(e)}")

        # Fall back to the original render method
        return original_render(self, template, **kwargs)

    # Apply the patch
    AuthDBView.render_template = patched_render_template

    # Make sure our custom security manager is registered and is being used
    if hasattr(app, "appbuilder") and hasattr(app.appbuilder, "sm"):
        sm = app.appbuilder.sm

        if isinstance(sm, CustomSecurityManager):
            sm.registerView()
        else:
            logger.warning(
                f"Security manager is not CustomSecurityManager, found: {type(sm).__name__}"
            )
            logger.warning("Attempting to replace security manager")
            try:
                # Try to recreate app builder with our security manager
                app.appbuilder._security_manager = CustomSecurityManager(app.appbuilder)
                app.appbuilder._security_manager.registerView()
            except Exception as e:
                logger.error(f"Failed to replace security manager: {str(e)}")


CUSTOM_SECURITY_MANAGER = CustomSecurityManager

# Register the app mutator
FLASK_APP_MUTATOR = init_app


# Add template folder settings that Flask-AppBuilder will use
FAB_TEMPLATE_FOLDER = TEMPLATES_DIR
APP_TEMPLATE_FOLDER = TEMPLATES_DIR
TEMPLATE_FOLDER = TEMPLATES_DIR

# Basic App Config
APP_NAME = "Ajna by Mason Labs"
APP_ICON = "/custom_static/images/mason-logo-128.png"
# SECRET_KEY = "Heg9eUGhHR90oYsD6qxYCJF/PDLWbor4E4L/A8rYGRAUX3clNxqljJxz"
SECRET_KEY = "dhbouYiha0Jvd9ScOhjnPILnA28xsOu/5jGWfl14sEDf6ZJtu291drE1"

ENABLE_PROXY_FIX = True
PREFERRED_URL_SCHEME = "https"

# Enable debug mode for Flask
# FLASK_DEBUG = True

# Template Settings
# TEMPLATES_AUTO_RELOAD = True

PREFERRED_DATABASES: List[str] = [
    "postgresql",
    "mysql",
    "sqlite",
    # Add other dialects you're actually using
    # Remove "gsheets", "shillelagh", etc. if you don't need them
]
