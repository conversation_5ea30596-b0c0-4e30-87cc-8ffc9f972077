{% extends 'appbuilder/base.html' %}
{% import 'appbuilder/general/lib.html' as lib %}

{% block head %}
{{ super() }}
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
<style>
/* Mobile-responsive styles */
@media (max-width: 374px) {
  .login-container { padding: 12px !important; }
  .login-card { padding: 20px !important; }
  .logo { width: 40px !important; height: 40px !important; }
  .logo svg { width: 20px !important; height: 20px !important; }
}

@media (min-width: 768px) {
  .oauth-container {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 12px !important;
  }
}

@media (min-width: 1024px) {
  .login-card { max-width: 480px !important; padding: 40px !important; }
  .logo { width: 56px !important; height: 56px !important; }
  .logo svg { width: 28px !important; height: 28px !important; }
}

/* Focus states */
input:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Hover states */
button:hover, .btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Hide header on login page */
header { display: none !important; }
</style>
{% endblock %}

{% block content %}
<div class="login-container" style="min-height: 100vh; width: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column; padding: 16px; position: relative; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
  <!-- Main login card -->
  <div class="login-card" style="border: 1px solid #e5e7eb; background: white; width: 100%; max-width: 400px; padding: 32px; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); display: flex; flex-direction: column; align-items: center; justify-content: center; margin: 16px 0;">
    <!-- Logo and Title -->
    <div class="logo" style="background-color: #1a1a1a; width: 48px; height: 48px; border-radius: 12px; display: inline-flex; justify-content: center; align-items: center; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" style="width: 24px; height: 24px;">
        <!-- Black Circle -->
        <circle cx="100" cy="100" r="90" fill="black"/>

        <!-- Orange Triangle (#E65C2E) -->
        <polygon points="100,30 160,140 40,140" fill="#E65C2E"/>
      </svg>
    </div>
    <h3 style="font-weight: 600; margin-bottom: 6px; font-size: 1.5rem; text-align: center; color: #111827; letter-spacing: -0.025em;">Xandro</h3>
    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 32px; text-align: center; line-height: 1.5; font-weight: 400;">The spark for growth lies in your data</p>

    <!-- Database Login Section -->
    {% if form_action %}
    <form style="width: 100%; display: flex; flex-direction: column;" role="form" action="{{form_action}}" method="post" enctype="multipart/form-data">
      {% else %}
      <form style="width: 100%; display: flex; flex-direction: column;" role="form" action="/login/" method="post" enctype="multipart/form-data">
        {% endif %}
        <input type="hidden" name="csrf_token" value="{{ csrf_token() if csrf_token else '' }}">

        <div style="margin-bottom: 20px;">
          <div>
            <label for="username" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Email</label>
          </div>
          <input style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;" id="username" name="username" type="text"
                 placeholder="<EMAIL>">
        </div>

        <div style="margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <label for="password" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Password</label>
            <a href="#" style="color: #3b82f6; text-decoration: none; font-size: 0.85rem; font-weight: 500; transition: color 0.2s ease;">Forgot password?</a>
          </div>
          <div style="position: relative;">
            <input style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;" id="password" name="password" type="password"
                   placeholder="••••••••">
            <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s ease; display: flex; align-items: center; justify-content: center;">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12 5C7 5 2.73 8.11 1 12.5C2.73 16.89 7 20 12 20C17 20 21.27 16.89 23 12.5C21.27 8.11 17 5 12 5ZM12 17.5C9.24 17.5 7 15.26 7 12.5C7 9.74 9.24 7.5 12 7.5C14.76 7.5 17 9.74 17 12.5C17 15.26 14.76 17.5 12 17.5ZM12 9.5C10.34 9.5 9 10.84 9 12.5C9 14.16 10.34 15.5 12 15.5C13.66 15.5 15 14.16 15 12.5C15 10.84 13.66 9.5 12 9.5Z"
                  fill="#888"/>
              </svg>
            </span>
          </div>
        </div>

        <div class="form-check mb-4" style="display: none;">
          <input class="form-check-input" id="remember_me" name="remember_me" type="checkbox" value="y" checked>
          <label class="form-check-label" for="remember_me">
            {{ _('Remember me') }}
          </label>
        </div>

        <button type="submit" style="border-radius: 6px; padding: 12px 20px; font-size: 15px; font-weight: 500; border: none; margin-top: 16px; background: #3b82f6; color: white; cursor: pointer; transition: all 0.2s ease; min-height: 44px; width: 100%; letter-spacing: 0.025em; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);">
          Sign in
        </button>
      </form>

      <div style="display: flex; align-items: center; justify-content: center; margin: 20px 0 16px 0;">
        <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
        <span style="margin: 20px 0 16px 0; color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; padding: 0 12px; white-space: nowrap;">OR CONTINUE WITH</span>
        <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
      </div>

      <!-- OAuth Providers Section -->
      <div class="oauth-container" style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
        {% if providers %}
        {% for provider in providers %}
        {% if provider.name|lower == 'google' %}
        <div style="width: 100%;">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             style="border-radius: 6px; padding: 11px 16px; font-size: 14px; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none; color: #374151; background: white; transition: all 0.2s ease; min-height: 44px; font-weight: 500; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); width: 100%;"
             class="btn btn-outline-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 186.69 190.5">
              <g transform="translate(1184.583 765.171)">
                <path clip-path="none" mask="none"
                      d="M-1089.333-687.239v36.888h51.262c-2.251 11.863-9.006 21.908-19.137 28.662l30.913 23.986c18.011-16.625 28.402-41.044 28.402-70.052 0-6.754-.606-13.249-1.732-19.483z"
                      fill="#4285f4"/>
                <path clip-path="none" mask="none"
                      d="M-1142.714-651.791l-6.972 5.337-24.679 19.223h0c15.673 31.086 47.796 52.561 85.03 52.561 25.717 0 47.278-8.486 63.038-23.033l-30.913-23.986c-8.486 5.715-19.31 9.179-32.125 9.179-24.765 0-45.806-16.712-53.34-39.226z"
                      fill="#34a853"/>
                <path clip-path="none" mask="none"
                      d="M-1174.365-712.61c-6.494 12.815-10.217 27.276-10.217 42.689s3.723 29.874 10.217 42.689c0 .086 31.693-24.592 31.693-24.592-1.905-5.715-3.031-11.776-3.031-18.098s1.126-12.383 3.031-18.098z"
                      fill="#fbbc05"/>
                <path
                  d="M-1089.333-727.244c14.028 0 26.497 4.849 36.455 14.201l27.276-27.276c-16.539-15.413-38.013-24.852-63.731-24.852-37.234 0-69.359 21.388-85.032 52.561l31.692 24.592c7.533-22.514 28.575-39.226 53.34-39.226z"
                  fill="#ea4335" clip-path="none" mask="none"/>
              </g>
            </svg>
            Google
          </a>
        </div>
        {% elif provider.name|lower == 'github' %}
        <div style="width: 100%;">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             style="border-radius: 6px; padding: 11px 16px; font-size: 14px; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none; color: #374151; background: white; transition: all 0.2s ease; min-height: 44px; font-weight: 500; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); width: 100%;"
             class="btn btn-outline-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </a>
        </div>
        {% else %}
        <div style="width: 100%;">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             style="border-radius: 6px; padding: 11px 16px; font-size: 14px; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none; color: #374151; background: white; transition: all 0.2s ease; min-height: 44px; font-weight: 500; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); width: 100%;"
             class="btn btn-outline-secondary">
            <i class="fa fa-{{ provider.name|lower }}"></i> {{ provider.name }}
          </a>
        </div>
        {% endif %}
        {% endfor %}
        {% endif %}
      </div>

      {% if appbuilder.sm.auth_user_registration %}
      <div style="text-align: center; margin-top: 20px;">
        <p style="font-size: 16px; margin: 8px 0 0 0; color: #6b7280;">
          Don't have an account?
          <a href="{{appbuilder.sm.get_url_for_registeruser}}" style="color: #3b82f6; text-decoration: none; font-weight: 600; transition: color 0.2s ease;">
            Sign up
          </a>
        </p>
      </div>
      {% endif %}
  </div>

  <!-- Footer with version info -->
  <div style="text-align: center; margin-top: 24px;">
    <p style="font-size: 0.8rem; color: #9ca3af; text-align: center; margin-top: 24px;">© 2025 Mason Labs. All rights reserved.</p>
  </div>
</div>

<script nonce="{{ csp_nonce() }}">
  // Your custom_login.js code goes here
  document.addEventListener('DOMContentLoaded', function () {
    // Your initialization code
    console.log('Custom login script loaded');
    // ...rest of your custom_login.js code...
    console.log('Custom login script loaded from /static directory');

    // Force login form container to be visible
    var dbLoginPanel = document.getElementById('db-login-panel');
    if (dbLoginPanel) {
      console.log('Found database login panel, ensuring visibility');
      dbLoginPanel.style.display = 'block';
      dbLoginPanel.style.visibility = 'visible';
      dbLoginPanel.style.opacity = '1';
    }

    // Ensure all form elements are visible
    var formElements = document.querySelectorAll('form input, form button');
    for (var i = 0; i < formElements.length; i++) {
      var element = formElements[i];
      element.style.display = 'inline-block';
      element.style.visibility = 'visible';
      element.style.opacity = '1';
      console.log('Enhanced visibility for form element: ' + (element.name || element.id || 'unnamed'));
    }

    console.log('Login form visibility enhancement complete');
  });
</script>
{% endblock %}