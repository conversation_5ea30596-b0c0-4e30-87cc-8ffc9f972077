{% extends 'appbuilder/base.html' %}
{% import 'appbuilder/general/lib.html' as lib %}

{% block content %}
<div class="lg-container">
  <!-- Main login card -->
  <div class="lg-card">
    <!-- Logo and Title -->
    <div
      class="lg-logo">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
        <!-- Black Circle -->
        <circle cx="100" cy="100" r="90" fill="black"/>

        <!-- Orange Triangle (#E65C2E) -->
        <polygon points="100,30 160,140 40,140" fill="#E65C2E"/>
      </svg>
    </div>
    <h3 style="font-weight: 600; margin-bottom: 15px;">Xandro</h3>
    <p style="color: #666; font-size: 1.1rem; margin-bottom: 30px;">The spark for growth lies in your data</p>

    <!-- Database Login Section -->
    {% if form_action %}
    <form class="lg-form" role="form" action="{{form_action}}" method="post" enctype="multipart/form-data">
      {% else %}
      <form class="lg-form" role="form" action="/login/" method="post" enctype="multipart/form-data">
        {% endif %}
        <input type="hidden" name="csrf_token" value="{{ csrf_token() if csrf_token else '' }}">

        <div class="lg-form-field">
          <div>
            <label for="username" class="lg-form-label">Email</label>
          </div>
          <input class="lg-form-input" id="username" name="username" type="text"
                 placeholder="<EMAIL>">
        </div>

        <div class="lg-form-field">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <label for="password" class="lg-form-label">Password</label>
            <a href="#" style="">Forgot password?</a>
          </div>
          <div style="position: relative;">
            <input class="lg-form-input" id="password" name="password" type="password"
                   placeholder="••••••••">
            <span style="position: absolute; right: 12px; top: 12px; cursor: pointer;">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path
                                              d="M12 5C7 5 2.73 8.11 1 12.5C2.73 16.89 7 20 12 20C17 20 21.27 16.89 23 12.5C21.27 8.11 17 5 12 5ZM12 17.5C9.24 17.5 7 15.26 7 12.5C7 9.74 9.24 7.5 12 7.5C14.76 7.5 17 9.74 17 12.5C17 15.26 14.76 17.5 12 17.5ZM12 9.5C10.34 9.5 9 10.84 9 12.5C9 14.16 10.34 15.5 12 15.5C13.66 15.5 15 14.16 15 12.5C15 10.84 13.66 9.5 12 9.5Z"
                                              fill="#888"/>
                                        </svg>
                                    </span>
          </div>
        </div>

        <div class="form-check mb-4" style="display: none;">
          <input class="form-check-input" id="remember_me" name="remember_me" type="checkbox" value="y" checked>
          <label class="form-check-label" for="remember_me">
            {{ _('Remember me') }}
          </label>
        </div>

        <button type="submit" class="lg-button">
          Sign in
        </button>
      </form>

      <div class="text-center mt-4 mb-3">
        <div style="display: flex; align-items: center; justify-content: center;">
          <div style="flex-grow: 1; height: 1px; background-color: #ddd;"></div>
          <span class="lg-continue">OR CONTINUE WITH</span>
          <div style="flex-grow: 1; height: 1px; background-color: #ddd;"></div>
        </div>
      </div>

      <!-- OAuth Providers Section -->
      <div class="row mb-4 g-2">
        {% if providers %}
        {% for provider in providers %}
        {% if provider.name|lower == 'google' %}
        <div class="col-6">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             class="btn btn-outline-secondary w-100"
             style="border-radius: 6px; padding: 10px; font-size: 16px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none;">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 186.69 190.5">
              <g transform="translate(1184.583 765.171)">
                <path clip-path="none" mask="none"
                      d="M-1089.333-687.239v36.888h51.262c-2.251 11.863-9.006 21.908-19.137 28.662l30.913 23.986c18.011-16.625 28.402-41.044 28.402-70.052 0-6.754-.606-13.249-1.732-19.483z"
                      fill="#4285f4"/>
                <path clip-path="none" mask="none"
                      d="M-1142.714-651.791l-6.972 5.337-24.679 19.223h0c15.673 31.086 47.796 52.561 85.03 52.561 25.717 0 47.278-8.486 63.038-23.033l-30.913-23.986c-8.486 5.715-19.31 9.179-32.125 9.179-24.765 0-45.806-16.712-53.34-39.226z"
                      fill="#34a853"/>
                <path clip-path="none" mask="none"
                      d="M-1174.365-712.61c-6.494 12.815-10.217 27.276-10.217 42.689s3.723 29.874 10.217 42.689c0 .086 31.693-24.592 31.693-24.592-1.905-5.715-3.031-11.776-3.031-18.098s1.126-12.383 3.031-18.098z"
                      fill="#fbbc05"/>
                <path
                  d="M-1089.333-727.244c14.028 0 26.497 4.849 36.455 14.201l27.276-27.276c-16.539-15.413-38.013-24.852-63.731-24.852-37.234 0-69.359 21.388-85.032 52.561l31.692 24.592c7.533-22.514 28.575-39.226 53.34-39.226z"
                  fill="#ea4335" clip-path="none" mask="none"/>
              </g>
            </svg>
            Google
          </a>
        </div>
        {% elif provider.name|lower == 'github' %}
        <div class="col-6">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             class="btn btn-outline-secondary w-100"
             style="border-radius: 6px; padding: 10px; font-size: 16px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none;">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path
                d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            GitHub
          </a>
        </div>
        {% else %}
        <div class="col-{% if loop.index0 % 2 == 0 %}6{% else %}6{% endif %}">
          <a href="{{ url_for('AuthOAuthView.login', provider=provider.name) }}"
             class="btn btn-outline-secondary w-100"
             style="border-radius: 6px; padding: 10px; font-size: 16px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none;">
            <i class="fa fa-{{ provider.name|lower }}"></i> {{ provider.name }}
          </a>
        </div>
        {% endif %}
        {% endfor %}
        {% endif %}
      </div>

      {% if appbuilder.sm.auth_user_registration %}
      <div class="text-center mt-3">
        <p style="font-size: 16px; margin-top:8px">
          Don't have an account?
          <a href="{{appbuilder.sm.get_url_for_registeruser}}"
             style=" font-weight: 500; text-decoration: none;">
            Sign up
          </a>
        </p>
      </div>
      {% endif %}
  </div>

  <!-- Footer with version info -->
  <div class="text-center mt-3" style="margin-top: 20px;">
    <p class="text-muted medium">© 2025 Mason Labs. All rights reserved.</p>
  </div>
</div>

<!-- Add CSS for modern styling -->
<style>
  .lg-form-label {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 16px;
  }

  .lg-form-input {
    color: #000;
    text-decoration: none;
    border-radius: 6px;
    padding: 12px;
    font-size: 16px;
    border: 1px solid #ddd;
    width: 100%;
  }

  .lg-form-field {
    margin-bottom: 10px;
  }

  .lg-form {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .lg-card {
    border: none;
    background: white;
    width: 100%;
    max-width: 32rem;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .lg-logo {
    background-color: #1a1a1a;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .form-control:focus {
    border-color: #333;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
  }

  .btn-dark:hover {
    background-color: #333;
  }

  .lg-container {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
  }

  .lg-button {
    border-radius: 6px;
    padding: 12px;
    font-size: 16px;
    border: none;
    margin-top: 10px;
    background: rgb(37, 99, 235);
    color: white;
  }

  .lg-continue {
    margin: 15px 0;
    color: #666;
    font-size: 14px;
  }

  header {
    display: none !important;
  }
</style>

<script nonce="{{ csp_nonce() }}">
  // Your custom_login.js code goes here
  document.addEventListener('DOMContentLoaded', function () {
    // Your initialization code
    console.log('Custom login script loaded');
    // ...rest of your custom_login.js code...
    console.log('Custom login script loaded from /static directory');

    // Force login form container to be visible
    var dbLoginPanel = document.getElementById('db-login-panel');
    if (dbLoginPanel) {
      console.log('Found database login panel, ensuring visibility');
      dbLoginPanel.style.display = 'block';
      dbLoginPanel.style.visibility = 'visible';
      dbLoginPanel.style.opacity = '1';
    }

    // Ensure all form elements are visible
    var formElements = document.querySelectorAll('form input, form button');
    for (var i = 0; i < formElements.length; i++) {
      var element = formElements[i];
      element.style.display = 'inline-block';
      element.style.visibility = 'visible';
      element.style.opacity = '1';
      console.log('Enhanced visibility for form element: ' + (element.name || element.id || 'unnamed'));
    }

    console.log('Login form visibility enhancement complete');
  });
</script>
{% endblock %}