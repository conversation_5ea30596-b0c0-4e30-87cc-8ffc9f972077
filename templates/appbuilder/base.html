<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Mobile optimization meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#2563eb">
    
    <!-- Prevent zoom on input focus for iOS -->
    <meta name="format-detection" content="telephone=no">
    
    <title>{% block title %}{{ appbuilder.app_name }}{% endblock %}</title>
    
    <!-- Favicon -->
    {% if appbuilder.app_icon %}
    <link rel="icon" type="image/png" href="{{ appbuilder.app_icon }}">
    <link rel="apple-touch-icon" href="{{ appbuilder.app_icon }}">
    {% endif %}
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/custom_static/css/mobile-responsive.css">
    
    {% block head %}{% endblock %}
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/custom_static/css/mobile-responsive.css" as="style">
    
    <style>
        /* Critical CSS for above-the-fold content */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Prevent flash of unstyled content */
        .lg-container {
            opacity: 0;
            animation: fadeIn 0.3s ease-in-out forwards;
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
        
        /* Loading state */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Loading indicator -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
    </div>
    
    <!-- Main content -->
    <main role="main">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Mobile enhancement scripts -->
    <script src="/custom_static/js/mobile-enhancements.js"></script>

    <!-- Mobile optimization scripts -->
    <script>
        // Prevent zoom on double tap for iOS
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Optimize viewport for mobile devices
        function optimizeViewport() {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport && window.innerWidth <= 768) {
                // Ensure proper scaling on mobile
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }
        }
        
        // Handle orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(optimizeViewport, 100);
        });
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            optimizeViewport();
            
            // Show content after CSS is loaded
            const container = document.querySelector('.lg-container');
            if (container) {
                container.style.opacity = '1';
            }
            
            // Hide loading indicator
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }
        });
        
        // Handle form submission loading state
        document.addEventListener('submit', function(e) {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Signing in...';
            }
        });
        
        // Improve touch responsiveness
        document.addEventListener('touchstart', function() {}, {passive: true});
        document.addEventListener('touchmove', function() {}, {passive: true});
    </script>
    
    {% block tail %}{% endblock %}
</body>
</html>
