{% extends "appbuilder/base.html" %}

{% block head %}
{{ super() }}
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
<style>
/* Mobile-responsive styles */
@media (max-width: 374px) {
  .login-container { padding: 12px !important; }
  .login-card { padding: 20px !important; }
  .logo { width: 40px !important; height: 40px !important; }
  .logo svg { width: 20px !important; height: 20px !important; }
}

@media (min-width: 768px) {
  .oauth-container {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 12px !important;
  }
}

@media (min-width: 1024px) {
  .login-card { max-width: 480px !important; padding: 40px !important; }
  .logo { width: 56px !important; height: 56px !important; }
  .logo svg { width: 28px !important; height: 28px !important; }
}

/* Focus states */
input:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Hover states */
button:hover, .btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Hide header and navigation on login page */
header, nav, .navbar, .header, .top-nav { display: none !important; }
body { margin: 0 !important; padding: 0 !important; }

/* Ensure our container takes full screen */
.login-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
}
</style>
{% endblock %}

{% block content %}
<script>
// Immediately hide navigation
(function() {
  const style = document.createElement('style');
  style.textContent = `
    nav, .navbar, header, .header, .top-nav, .navigation {
      display: none !important;
      visibility: hidden !important;
    }
    body { margin: 0 !important; padding: 0 !important; }
  `;
  document.head.appendChild(style);
})();
</script>

<div class="login-container" style="min-height: 100vh; width: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column; padding: 16px; position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
  <!-- Main login card -->
  <div class="login-card" style="border: 1px solid #e5e7eb; background: white; width: 100%; max-width: 400px; padding: 32px; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); display: flex; flex-direction: column; align-items: center; justify-content: center; margin: 16px 0;">
    <!-- Logo and Title -->
    <div class="logo" style="background-color: #1a1a1a; width: 48px; height: 48px; border-radius: 12px; display: inline-flex; justify-content: center; align-items: center; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" style="width: 24px; height: 24px;">
        <!-- Black Circle -->
        <circle cx="100" cy="100" r="90" fill="black"/>

        <!-- Orange Triangle (#E65C2E) -->
        <polygon points="100,30 160,140 40,140" fill="#E65C2E"/>
      </svg>
    </div>
    <h3 style="font-weight: 600; margin-bottom: 6px; font-size: 1.5rem; text-align: center; color: #111827; letter-spacing: -0.025em;">Xandro</h3>
    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 32px; text-align: center; line-height: 1.5; font-weight: 400;">The spark for growth lies in your data</p>

    <!-- Database Login Section -->
    <form style="width: 100%; display: flex; flex-direction: column;" method="post" action="">
      {{ form.hidden_tag() }}

      <div style="margin-bottom: 20px;">
        <label for="username" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Email</label>
        {{ form.username(id="username", style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;", placeholder="<EMAIL>", class="") }}
      </div>

      <div style="margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <label for="password" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Password</label>
          <a href="#" style="color: #3b82f6; text-decoration: none; font-size: 0.85rem; font-weight: 500; transition: color 0.2s ease;">Forgot password?</a>
        </div>
        <div style="position: relative;">
          {{ form.password(id="password", style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;", placeholder="••••••••", class="") }}
          <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s ease; display: flex; align-items: center; justify-content: center;">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5C7 5 2.73 8.11 1 12.5C2.73 16.89 7 20 12 20C17 20 21.27 16.89 23 12.5C21.27 8.11 17 5 12 5ZM12 17.5C9.24 17.5 7 15.26 7 12.5C7 9.74 9.24 7.5 12 7.5C14.76 7.5 17 9.74 17 12.5C17 15.26 14.76 17.5 12 17.5ZM12 9.5C10.34 9.5 9 10.84 9 12.5C9 14.16 10.34 15.5 12 15.5C13.66 15.5 15 14.16 15 12.5C15 10.84 13.66 9.5 12 9.5Z" fill="#888"/>
            </svg>
          </span>
        </div>
      </div>

      <div style="display: none;">
        {{ form.remember_me() }}
      </div>

      <button type="submit" style="border-radius: 6px; padding: 12px 20px; font-size: 15px; font-weight: 500; border: none; margin-top: 16px; background: #3b82f6; color: white; cursor: pointer; transition: all 0.2s ease; min-height: 44px; width: 100%; letter-spacing: 0.025em; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);">
        Sign in
      </button>
    </form>

    <div style="display: flex; align-items: center; justify-content: center; margin: 20px 0 16px 0;">
      <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
      <span style="margin: 20px 0 16px 0; color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; padding: 0 12px; white-space: nowrap;">OR CONTINUE WITH</span>
      <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
    </div>

    <!-- OAuth Providers Section -->
    <div class="oauth-container" style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
      {% if providers %}
        {% for provider in providers %}
        <div style="width: 100%;">
          <a href="{{url_for('AuthDBView.login', provider=provider.name, next=request.args.get('next',''))}}"
             style="border-radius: 6px; padding: 11px 16px; font-size: 14px; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; gap: 8px; text-decoration: none; color: #374151; background: white; transition: all 0.2s ease; min-height: 44px; font-weight: 500; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); width: 100%;"
             class="btn">
            <i class="fa {{provider.icon}}"></i> {{provider.name}}
          </a>
        </div>
        {% endfor %}
      {% endif %}
    </div>

    <div style="text-align: center; margin-top: 20px;">
      <p style="font-size: 16px; margin: 8px 0 0 0; color: #6b7280;">
        Don't have an account?
        <a href="#" style="color: #3b82f6; text-decoration: none; font-weight: 600; transition: color 0.2s ease;">
          Sign up
        </a>
      </p>
    </div>
  </div>

  <!-- Footer with version info -->
  <div style="text-align: center; margin-top: 24px;">
    <p style="font-size: 0.8rem; color: #9ca3af; text-align: center; margin-top: 24px;">© 2025 Mason Labs. All rights reserved.</p>
  </div>
</div>

<script>
// Hide navigation and apply styles immediately
document.addEventListener('DOMContentLoaded', function() {
  // Hide all navigation elements
  const navElements = document.querySelectorAll('nav, .navbar, header, .header, .top-nav');
  navElements.forEach(el => {
    el.style.display = 'none';
  });

  // Ensure body has no margin/padding
  document.body.style.margin = '0';
  document.body.style.padding = '0';

  // Apply container styles
  const container = document.querySelector('.login-container');
  if (container) {
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.right = '0';
    container.style.bottom = '0';
    container.style.zIndex = '9999';
  }
});

// Also run immediately in case DOMContentLoaded already fired
const navElements = document.querySelectorAll('nav, .navbar, header, .header, .top-nav');
navElements.forEach(el => {
  el.style.display = 'none';
});
</script>
{% endblock %}