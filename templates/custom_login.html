{% extends "appbuilder/base.html" %}

{% block head %}
{{ super() }}
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
<style>
/* Mobile-responsive styles */
@media (max-width: 374px) {
  .login-container { padding: 12px !important; }
  .login-card { padding: 20px !important; }
  .logo { width: 40px !important; height: 40px !important; }
  .logo svg { width: 20px !important; height: 20px !important; }
}

@media (min-width: 768px) {
  .oauth-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
  }

  .oauth-container > div {
    width: 100% !important;
  }
}

@media (min-width: 1024px) {
  .login-card { max-width: 480px !important; padding: 40px !important; }
  .login-container { padding: 40px 16px !important; }
  .logo { width: 64px !important; height: 64px !important; border-radius: 18px !important; }
  .logo svg { width: 32px !important; height: 32px !important; }
}

/* Focus states */
input:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Hover states */
button:hover, .btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Hide header and navigation on login page */
header, nav, .navbar, .header, .top-nav { display: none !important; }
body { margin: 0 !important; padding: 0 !important; }

/* Ensure our container takes full screen */
.login-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
}
</style>
{% endblock %}

{% block content %}
<style>
/* Immediate navigation hiding - highest priority */
nav, .navbar, header, .header, .top-nav, .navigation,
.navbar-nav, .navbar-brand, .nav, .navbar-header,
[class*="nav"], [class*="header"], [class*="menu"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

/* Force our container to be on top */
.login-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 99999 !important;
}
</style>

<script nonce="{{ csp_nonce() }}">
// Immediately hide navigation with multiple methods
(function() {
  // Method 1: CSS injection
  const style = document.createElement('style');
  style.textContent = `
    nav, .navbar, header, .header, .top-nav, .navigation {
      display: none !important;
      visibility: hidden !important;
    }
    body { margin: 0 !important; padding: 0 !important; }
  `;
  document.head.appendChild(style);

  // Method 2: Direct DOM manipulation
  const hideElements = () => {
    const selectors = ['nav', '.navbar', 'header', '.header', '.top-nav', '.navigation', '.navbar-nav'];
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        el.style.display = 'none';
        el.style.visibility = 'hidden';
        el.style.opacity = '0';
        el.style.height = '0';
        el.style.overflow = 'hidden';
      });
    });
  };

  // Run immediately
  hideElements();

  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', hideElements);
  }

  // Run after a short delay to catch any dynamically added elements
  setTimeout(hideElements, 100);
  setTimeout(hideElements, 500);
})();
</script>

<div class="login-container" style="min-height: 100vh; width: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column; padding: 32px 16px; position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
  <!-- Main login card -->
  <div class="login-card" style="border: 1px solid #e5e7eb; background: white; width: 100%; max-width: 400px; padding: 32px; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); display: flex; flex-direction: column; align-items: center; justify-content: center; margin: 0;">
    <!-- Logo and Title -->
    <div class="logo" style="background-color: #1a1a1a; width: 48px; height: 48px; border-radius: 12px; display: inline-flex; justify-content: center; align-items: center; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" style="width: 24px; height: 24px;">
        <!-- Black Circle -->
        <circle cx="100" cy="100" r="90" fill="black"/>

        <!-- Orange Triangle (#E65C2E) -->
        <polygon points="100,30 160,140 40,140" fill="#E65C2E"/>
      </svg>
    </div>
    <h3 style="font-weight: 600; margin-bottom: 6px; font-size: 1.5rem; text-align: center; color: #111827; letter-spacing: -0.025em;">Xandro</h3>
    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 32px; text-align: center; line-height: 1.5; font-weight: 400;">The spark for growth lies in your data</p>

    <!-- Database Login Section -->
    <form style="width: 100%; display: flex; flex-direction: column;" method="post" action="">
      {{ form.hidden_tag() }}

      <div style="margin-bottom: 20px;">
        <label for="username" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Email</label>
        {{ form.username(id="username", style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;", placeholder="<EMAIL>", class="") }}
      </div>

      <div style="margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
          <label for="password" style="font-weight: 500; margin-bottom: 6px; font-size: 0.875rem; color: #374151; display: block; letter-spacing: 0.025em;">Password</label>
          <a href="#" style="color: #3b82f6; text-decoration: none; font-size: 0.85rem; font-weight: 500; transition: color 0.2s ease;">Forgot password?</a>
        </div>
        <div style="position: relative;">
          {{ form.password(id="password", style="color: #1f2937; border-radius: 6px; padding: 12px 16px; font-size: 15px; border: 1px solid #d1d5db; width: 100%; transition: all 0.2s ease; background-color: #ffffff; -webkit-appearance: none; appearance: none; font-weight: 400; line-height: 1.5;", placeholder="••••••••", class="") }}
          <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s ease; display: flex; align-items: center; justify-content: center;">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5C7 5 2.73 8.11 1 12.5C2.73 16.89 7 20 12 20C17 20 21.27 16.89 23 12.5C21.27 8.11 17 5 12 5ZM12 17.5C9.24 17.5 7 15.26 7 12.5C7 9.74 9.24 7.5 12 7.5C14.76 7.5 17 9.74 17 12.5C17 15.26 14.76 17.5 12 17.5ZM12 9.5C10.34 9.5 9 10.84 9 12.5C9 14.16 10.34 15.5 12 15.5C13.66 15.5 15 14.16 15 12.5C15 10.84 13.66 9.5 12 9.5Z" fill="#888"/>
            </svg>
          </span>
        </div>
      </div>

      <div style="display: none;">
        {{ form.remember_me() }}
      </div>

      <button type="submit" style="border-radius: 6px; padding: 12px 20px; font-size: 15px; font-weight: 500; border: none; margin-top: 16px; background: #3b82f6; color: white; cursor: pointer; transition: all 0.2s ease; min-height: 44px; width: 100%; letter-spacing: 0.025em; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);">
        Sign in
      </button>
    </form>

    <div style="display: flex; align-items: center; justify-content: center; margin: 20px 0 16px 0;">
      <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
      <span style="margin: 20px 0 16px 0; color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; padding: 0 12px; white-space: nowrap;">OR CONTINUE WITH</span>
      <div style="flex-grow: 1; height: 1px; background-color: #e5e7eb;"></div>
    </div>

    <!-- OAuth Providers Section -->
    <div class="oauth-container" style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
      {% if providers %}
        {% for provider in providers %}
        <div style="width: 100%; display: block;">
          <a href="{{url_for('AuthDBView.login', provider=provider.name, next=request.args.get('next',''))}}"
             style="border-radius: 6px; padding: 12px 20px; font-size: 15px; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; gap: 10px; text-decoration: none; color: #374151; background: white; transition: all 0.2s ease; min-height: 44px; font-weight: 500; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); width: 100%; box-sizing: border-box;"
             class="btn">
            {% if provider.name|lower == 'google' %}
              <svg width="18" height="18" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Continue with Google
            {% elif provider.name|lower == 'github' %}
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              Continue with GitHub
            {% else %}
              <i class="fa {{provider.icon}}"></i> Continue with {{provider.name}}
            {% endif %}
          </a>
        </div>
        {% endfor %}
      {% endif %}
    </div>

    <div style="text-align: center; margin-top: 20px;">
      <p style="font-size: 16px; margin: 8px 0 0 0; color: #6b7280;">
        Don't have an account?
        <a href="#" style="color: #3b82f6; text-decoration: none; font-weight: 600; transition: color 0.2s ease;">
          Sign up
        </a>
      </p>
    </div>
  </div>

  <!-- Footer with version info -->
  <div style="text-align: center; margin-top: 24px;">
    <p style="font-size: 0.8rem; color: #9ca3af; text-align: center; margin-top: 24px;">© 2025 Mason Labs. All rights reserved.</p>
  </div>
</div>

<script nonce="{{ csp_nonce() }}">
// Hide navigation and apply styles immediately
document.addEventListener('DOMContentLoaded', function() {
  // Hide all navigation elements
  const navElements = document.querySelectorAll('nav, .navbar, header, .header, .top-nav');
  navElements.forEach(el => {
    el.style.display = 'none';
  });

  // Ensure body has no margin/padding
  document.body.style.margin = '0';
  document.body.style.padding = '0';

  // Apply container styles
  const container = document.querySelector('.login-container');
  if (container) {
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.right = '0';
    container.style.bottom = '0';
    container.style.zIndex = '9999';
  }
});

// Also run immediately in case DOMContentLoaded already fired
const navElements = document.querySelectorAll('nav, .navbar, header, .header, .top-nav');
navElements.forEach(el => {
  el.style.display = 'none';
});
</script>
{% endblock %}