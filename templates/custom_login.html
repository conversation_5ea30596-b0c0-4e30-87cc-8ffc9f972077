{% extends "appbuilder/base.html" %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-6 col-md-offset-3">
            <h2>{{ title }}</h2>
            
            <!-- Database login form -->
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">Login with username and password</h3>
                </div>
                <div class="panel-body">
                    <form method="post" action="">
                        {{ form.hidden_tag() }}
                        <div class="form-group">
                            {{ form.username.label }}
                            {{ form.username(size=20, class="form-control", autofocus=true) }}
                        </div>
                        <div class="form-group">
                            {{ form.password.label }}
                            {{ form.password(size=20, class="form-control") }}
                        </div>
                        <div class="checkbox">
                            <label>
                                {{ form.remember_me() }} {{ form.remember_me.label }}
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
            
            <!-- OAuth Providers -->
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">Login with OAuth Provider</h3>
                </div>
                <div class="panel-body">
                    <div class="btn-group" style="width:100%;">
                        {% if providers %}
                            {% for provider in providers %}
                            <a href="{{url_for('AuthDBView.login', provider=provider.name, next=request.args.get('next',''))}}" class="btn btn-lg btn-default btn-block" role="button">
                                <i class="fa {{provider.icon}}"></i> {{provider.name}}
                            </a>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>
{% endblock %} 