#!/bin/bash

# Set environment variables
export SUPERSET_CONFIG_PATH=/Users/<USER>/Sandbox/Mason/ajna-frontend/superset_config.py

# Make sure required directories exist
mkdir -p ./exports
mkdir -p ./imports/temp_import

# Function to export all assets
function export_all() {
  TIMESTAMP=$(date +%Y%m%d_%H%M%S)
  EXPORT_DIR="./exports/${TIMESTAMP}"
  mkdir -p $EXPORT_DIR

  echo "Exporting dashboards..."
  superset export-dashboards -f "${EXPORT_DIR}/dashboards.zip"
  
  echo "Exporting datasources..."
  superset export-datasources -f "${EXPORT_DIR}/datasources.zip"
  
  # Create a zip archive of the exports
  cd ./exports
  zip -r "${TIMESTAMP}_superset_export.zip" "${TIMESTAMP}"
  cd ..
  
  echo "Export completed and saved to ${EXPORT_DIR} and as a zip archive."
  echo "Archive location: ./exports/${TIMESTAMP}_superset_export.zip"
}

# Function to import assets
function import_all() {
  if [ -z "$1" ]; then
    echo "Please provide the path to the export directory or zip file."
    exit 1
  fi

  IMPORT_PATH=$1
  
  # If it's a top-level zip file, extract it
  if [[ $IMPORT_PATH == *.zip && ! $IMPORT_PATH == */datasources.zip && ! $IMPORT_PATH == */dashboards.zip ]]; then
    EXTRACT_DIR="./imports/temp_import"
    mkdir -p $EXTRACT_DIR
    unzip -o $IMPORT_PATH -d $EXTRACT_DIR
    # Try to find the timestamp directory within the extracted contents
    POTENTIAL_DIR=$(find $EXTRACT_DIR -type d -maxdepth 1 | grep -v "^$EXTRACT_DIR$" | head -n 1)
    if [ -n "$POTENTIAL_DIR" ]; then
      IMPORT_PATH=$POTENTIAL_DIR
    else
      IMPORT_PATH=$EXTRACT_DIR
    fi
  fi
  
  # Find the zip files
  DATASOURCES_FILE=$(find $IMPORT_PATH -name "datasources.zip" | head -n 1)
  DASHBOARDS_FILE=$(find $IMPORT_PATH -name "dashboards.zip" | head -n 1)
  
  # Import in the correct order - datasources first, then dashboards
  if [ -f "$DATASOURCES_FILE" ]; then
    echo "Importing datasources from $DATASOURCES_FILE"
    superset import-datasources -p $DATASOURCES_FILE
  else
    echo "Warning: No datasources.zip found in $IMPORT_PATH"
  fi
  
  if [ -f "$DASHBOARDS_FILE" ]; then
    echo "Importing dashboards from $DASHBOARDS_FILE"
    superset import-dashboards -p $DASHBOARDS_FILE
  else
    echo "Warning: No dashboards.zip found in $IMPORT_PATH"
  fi
  
  # Clean up if we extracted a zip
  if [[ -d "./imports/temp_import" ]]; then
    rm -rf "./imports/temp_import"
  fi
  
  echo "Import completed."
}

# Check what commands are available (for debugging)
function list_commands() {
  echo "Available Superset commands related to export/import:"
  superset --help | grep -E "export|import"
}

# Check command-line arguments
case "$1" in
  export)
    export_all
    ;;
  import)
    import_all $2
    ;;
  list)
    list_commands
    ;;
  *)
    echo "Usage: $0 {export|import [path_to_import_file_or_dir]|list}"
    echo ""
    echo "  export                Export dashboards and datasources to zip files"
    echo "  import [path]         Import dashboards and datasources from exported files"
    echo "  list                  List available export/import commands in your Superset version"
    exit 1
esac

exit 0