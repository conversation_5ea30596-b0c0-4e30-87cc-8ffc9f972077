# Ajna Frontend - Apache Superset Customization

This repository contains a customized implementation of Apache Superset for the Ajna project, providing a powerful business intelligence and data visualization platform with custom authentication, styling, and functionality.

## Overview

Ajna Frontend is a customized version of Apache Superset that integrates with the Ajna Metrics semantic layer to provide business intelligence and analytics capabilities. It features custom authentication, UI modifications, and specific configurations for ecommerce analytics.

## Features

- Custom OAuth authentication with Google integration
- Customized login interface
- Modified UI templates and styling
- Integration with Ajna Metrics semantic layer
- Custom security manager for enhanced access control
- Support for both database and OAuth authentication

## Project Structure

```
ajna-frontend/
├── custom_static/        # Custom static assets (CSS, JS, images)
├── templates/           # Custom Jinja templates for UI modifications
├── superset_config.py  # Main Superset configuration file
├── .env               # Environment variables (private)
├── .env.example      # Example environment variables template
└── superset.db      # SQLite database for Superset metadata
```

## Prerequisites

- Python 3.11 (specified in `.python-version`)
- Virtual environment (recommended)
- Access to Ajna Metrics semantic layer
- Google OAuth credentials (for authentication)

## Setup

1. Install `uv` if you haven't already:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create and activate a Python virtual environment using `uv`:
```bash
uv venv
source .venv/bin/activate  # On Unix/macOS
# or
.venv\Scripts\activate     # On Windows
```

3. Copy the environment template and configure your variables:
```bash
cp .env.example .env
```

4. Edit the `.env` file with your configuration:
- OAuth credentials
- Database connection details
- Other environment-specific settings

5. Install dependencies using `uv`:
```bash
uv pip install -r requirements.txt
```

6. Set the SUPERSET_CONFIG_PATH environment variable to point to your configuration file:
```bash
export SUPERSET_CONFIG_PATH=/Users/<USER>/kubric-codebase/ajna-frontend/superset_config.py
```

> **Important**: The SUPERSET_CONFIG_PATH environment variable must be set before running Superset. This tells Superset where to find its configuration file. Without this, Superset will use default settings and may show warnings about insecure configurations.

## Configuration

The main configuration is handled through `superset_config.py`, which includes:
- OAuth authentication settings
- Custom security manager configuration
- Template and static file handling
- Database connections
- Feature flags and permissions

## Customization

### Templates
Custom Jinja templates in the `templates/` directory modify the default Superset UI:
- Custom login page
- Modified dashboard layouts
- Custom visualization components

### Static Assets
Custom static assets in `custom_static/` include:
- Custom CSS styles
- JavaScript modifications
- Custom images and icons

## Development

1. Start the development server:
```bash
superset run -p 8088 --with-threads --reload --debugger
```

2. Access the application at `http://localhost:8088`

## User Management

### Creating Admin Users

To create an admin user for Superset login, run:
```bash
superset fab create-admin
```

This command will prompt you to enter:
- Username
- First name
- Last name
- Email
- Password

Example:
```
Username: admin
First name: Admin
Last name: User
Email: <EMAIL>
Password: ********
```

> **Note**: Make sure to use a strong password and keep your credentials secure. The admin user will have full access to all Superset features.

## Integration with Ajna Metrics

This frontend integrates with the Ajna Metrics semantic layer to provide:
- Standardized ecommerce metrics
- Cross-platform analytics
- Pre-aggregated data views
- Unified data access

## Security

- OAuth authentication with Google
- Custom security manager for fine-grained access control
- Database authentication fallback
- Role-based access control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

[Add your license information here]

## Support

For support and questions, please contact the development team or refer to the Ajna Metrics documentation. 