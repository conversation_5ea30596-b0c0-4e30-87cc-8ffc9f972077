# This file was autogenerated by uv via the following command:
#    uv pip compile --output-file=requirements.lock requirements.txt
alembic==1.15.1
    # via
    #   -r requirements.txt
    #   flask-migrate
amqp==5.3.1
    # via
    #   -r requirements.txt
    #   kombu
apache-superset==4.1.1
    # via -r requirements.txt
apispec==6.8.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
apsw==********
    # via
    #   -r requirements.txt
    #   shillelagh
attrs==25.1.0
    # via
    #   -r requirements.txt
    #   cattrs
    #   jsonschema
    #   outcome
    #   referencing
    #   requests-cache
    #   trio
authlib==1.5.1
    # via -r requirements.txt
babel==2.17.0
    # via
    #   -r requirements.txt
    #   flask-babel
backoff==2.2.1
    # via
    #   -r requirements.txt
    #   apache-superset
bcrypt==4.3.0
    # via
    #   -r requirements.txt
    #   paramiko
billiard==4.2.1
    # via
    #   -r requirements.txt
    #   celery
blinker==1.9.0
    # via
    #   -r requirements.txt
    #   flask
bottleneck==1.4.2
    # via
    #   -r requirements.txt
    #   pandas
brotli==1.1.0
    # via
    #   -r requirements.txt
    #   flask-compress
cachelib==0.13.0
    # via
    #   -r requirements.txt
    #   flask-caching
    #   flask-session
cachetools==5.5.2
    # via
    #   -r requirements.txt
    #   google-auth
cattrs==24.1.2
    # via
    #   -r requirements.txt
    #   requests-cache
celery==5.4.0
    # via
    #   -r requirements.txt
    #   apache-superset
certifi==2025.1.31
    # via
    #   -r requirements.txt
    #   requests
    #   selenium
cffi==1.17.1
    # via
    #   -r requirements.txt
    #   cryptography
    #   pynacl
charset-normalizer==3.4.1
    # via
    #   -r requirements.txt
    #   requests
click==8.1.8
    # via
    #   -r requirements.txt
    #   apache-superset
    #   celery
    #   click-didyoumean
    #   click-option-group
    #   click-plugins
    #   click-repl
    #   flask
    #   flask-appbuilder
click-didyoumean==0.3.1
    # via
    #   -r requirements.txt
    #   celery
click-option-group==0.5.6
    # via
    #   -r requirements.txt
    #   apache-superset
click-plugins==1.1.1
    # via
    #   -r requirements.txt
    #   celery
click-repl==0.3.0
    # via
    #   -r requirements.txt
    #   celery
colorama==0.4.6
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
cron-descriptor==1.4.5
    # via
    #   -r requirements.txt
    #   apache-superset
croniter==6.0.0
    # via
    #   -r requirements.txt
    #   apache-superset
cryptography==42.0.8
    # via
    #   -r requirements.txt
    #   apache-superset
    #   authlib
    #   paramiko
    #   pyopenssl
deprecated==1.2.18
    # via
    #   -r requirements.txt
    #   limits
deprecation==2.1.0
    # via
    #   -r requirements.txt
    #   apache-superset
dnspython==2.7.0
    # via
    #   -r requirements.txt
    #   email-validator
email-validator==2.2.0
    # via
    #   -r requirements.txt
    #   flask-appbuilder
flask==2.3.3
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
    #   flask-babel
    #   flask-caching
    #   flask-compress
    #   flask-jwt-extended
    #   flask-limiter
    #   flask-login
    #   flask-migrate
    #   flask-session
    #   flask-sqlalchemy
    #   flask-wtf
flask-appbuilder==4.6.0
    # via
    #   -r requirements.txt
    #   apache-superset
flask-babel==2.0.0
    # via
    #   -r requirements.txt
    #   flask-appbuilder
flask-caching==2.3.1
    # via
    #   -r requirements.txt
    #   apache-superset
flask-compress==1.17
    # via
    #   -r requirements.txt
    #   apache-superset
flask-jwt-extended==4.7.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
flask-limiter==3.11.0
    # via
    #   -r requirements.txt
    #   flask-appbuilder
flask-login==0.6.3
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
flask-migrate==3.1.0
    # via
    #   -r requirements.txt
    #   apache-superset
flask-session==0.8.0
    # via
    #   -r requirements.txt
    #   apache-superset
flask-sqlalchemy==2.5.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
    #   flask-migrate
flask-talisman==1.1.0
    # via
    #   -r requirements.txt
    #   apache-superset
flask-wtf==1.2.2
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
func-timeout==4.3.5
    # via
    #   -r requirements.txt
    #   apache-superset
geographiclib==2.0
    # via
    #   -r requirements.txt
    #   geopy
geopy==2.4.1
    # via
    #   -r requirements.txt
    #   apache-superset
google-auth==2.38.0
    # via
    #   -r requirements.txt
    #   shillelagh
greenlet==3.1.1
    # via
    #   -r requirements.txt
    #   shillelagh
gunicorn==23.0.0
    # via
    #   -r requirements.txt
    #   apache-superset
h11==0.14.0
    # via
    #   -r requirements.txt
    #   wsproto
hashids==1.3.1
    # via
    #   -r requirements.txt
    #   apache-superset
holidays==0.25
    # via
    #   -r requirements.txt
    #   apache-superset
humanize==4.12.1
    # via
    #   -r requirements.txt
    #   apache-superset
idna==3.10
    # via
    #   -r requirements.txt
    #   email-validator
    #   requests
    #   trio
importlib-metadata==8.6.1
    # via
    #   -r requirements.txt
    #   apache-superset
isodate==0.7.2
    # via
    #   -r requirements.txt
    #   apache-superset
itsdangerous==2.2.0
    # via
    #   -r requirements.txt
    #   flask
    #   flask-wtf
jinja2==3.1.6
    # via
    #   -r requirements.txt
    #   flask
    #   flask-babel
jsonpath-ng==1.7.0
    # via
    #   -r requirements.txt
    #   apache-superset
jsonschema==4.23.0
    # via
    #   -r requirements.txt
    #   flask-appbuilder
jsonschema-specifications==2024.10.1
    # via
    #   -r requirements.txt
    #   jsonschema
kombu==5.4.2
    # via
    #   -r requirements.txt
    #   celery
korean-lunar-calendar==0.3.1
    # via
    #   -r requirements.txt
    #   holidays
limits==4.2
    # via
    #   -r requirements.txt
    #   flask-limiter
llvmlite==0.43.0
    # via
    #   -r requirements.txt
    #   numba
mako==1.3.9
    # via
    #   -r requirements.txt
    #   alembic
    #   apache-superset
markdown==3.7
    # via
    #   -r requirements.txt
    #   apache-superset
markdown-it-py==3.0.0
    # via
    #   -r requirements.txt
    #   rich
markupsafe==3.0.2
    # via
    #   -r requirements.txt
    #   jinja2
    #   mako
    #   werkzeug
    #   wtforms
marshmallow==3.26.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
    #   marshmallow-sqlalchemy
marshmallow-sqlalchemy==1.4.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
mdurl==0.1.2
    # via
    #   -r requirements.txt
    #   markdown-it-py
msgpack==1.0.8
    # via
    #   -r requirements.txt
    #   apache-superset
msgspec==0.19.0
    # via
    #   -r requirements.txt
    #   flask-session
nh3==0.2.21
    # via
    #   -r requirements.txt
    #   apache-superset
numba==0.60.0
    # via
    #   -r requirements.txt
    #   pandas
numexpr==2.10.2
    # via
    #   -r requirements.txt
    #   pandas
numpy==1.23.5
    # via
    #   -r requirements.txt
    #   apache-superset
    #   bottleneck
    #   numba
    #   numexpr
    #   pandas
    #   pyarrow
ordered-set==4.1.0
    # via
    #   -r requirements.txt
    #   flask-limiter
outcome==1.3.0.post0
    # via
    #   -r requirements.txt
    #   trio
    #   trio-websocket
packaging==24.2
    # via
    #   -r requirements.txt
    #   apache-superset
    #   apispec
    #   deprecation
    #   gunicorn
    #   limits
    #   marshmallow
    #   shillelagh
pandas==2.0.3
    # via
    #   -r requirements.txt
    #   apache-superset
paramiko==3.5.1
    # via
    #   -r requirements.txt
    #   apache-superset
    #   sshtunnel
parsedatetime==2.6
    # via
    #   -r requirements.txt
    #   apache-superset
pgsanity==0.2.9
    # via
    #   -r requirements.txt
    #   apache-superset
pillow==11.1.0
    # via -r requirements.txt
platformdirs==4.3.6
    # via
    #   -r requirements.txt
    #   requests-cache
ply==3.11
    # via
    #   -r requirements.txt
    #   jsonpath-ng
polyline==2.0.2
    # via
    #   -r requirements.txt
    #   apache-superset
prison==0.2.1
    # via
    #   -r requirements.txt
    #   flask-appbuilder
prompt-toolkit==3.0.50
    # via
    #   -r requirements.txt
    #   click-repl
psycopg2==2.9.10
    # via -r requirements.txt
pyarrow==14.0.2
    # via
    #   -r requirements.txt
    #   apache-superset
pyasn1==0.6.1
    # via
    #   -r requirements.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via
    #   -r requirements.txt
    #   google-auth
pycparser==2.22
    # via
    #   -r requirements.txt
    #   cffi
pygments==2.19.1
    # via
    #   -r requirements.txt
    #   rich
pyjwt==2.10.1
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
    #   flask-jwt-extended
pynacl==1.5.0
    # via
    #   -r requirements.txt
    #   paramiko
pyopenssl==25.0.0
    # via
    #   -r requirements.txt
    #   shillelagh
pyparsing==3.2.1
    # via
    #   -r requirements.txt
    #   apache-superset
pysocks==1.7.1
    # via
    #   -r requirements.txt
    #   urllib3
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.txt
    #   apache-superset
    #   celery
    #   croniter
    #   flask-appbuilder
    #   holidays
    #   pandas
    #   shillelagh
python-dotenv==1.0.1
    # via
    #   -r requirements.txt
    #   apache-superset
python-geohash==0.8.5
    # via
    #   -r requirements.txt
    #   apache-superset
pytz==2025.1
    # via
    #   -r requirements.txt
    #   croniter
    #   flask-babel
    #   pandas
pyyaml==6.0.2
    # via
    #   -r requirements.txt
    #   apache-superset
    #   apispec
redis==4.6.0
    # via
    #   -r requirements.txt
    #   apache-superset
referencing==0.36.2
    # via
    #   -r requirements.txt
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   -r requirements.txt
    #   requests-cache
    #   shillelagh
requests-cache==1.2.1
    # via
    #   -r requirements.txt
    #   shillelagh
rich==13.9.4
    # via
    #   -r requirements.txt
    #   flask-limiter
rpds-py==0.23.1
    # via
    #   -r requirements.txt
    #   jsonschema
    #   referencing
rsa==4.9
    # via
    #   -r requirements.txt
    #   google-auth
selenium==4.9.1
    # via
    #   -r requirements.txt
    #   apache-superset
setuptools==76.0.0
    # via -r requirements.txt
shillelagh==1.3.4
    # via
    #   -r requirements.txt
    #   apache-superset
shortid==0.1.2
    # via
    #   -r requirements.txt
    #   apache-superset
simplejson==3.20.1
    # via
    #   -r requirements.txt
    #   apache-superset
six==1.17.0
    # via
    #   -r requirements.txt
    #   prison
    #   python-dateutil
    #   url-normalize
    #   wtforms-json
slack-sdk==3.34.0
    # via
    #   -r requirements.txt
    #   apache-superset
sniffio==1.3.1
    # via
    #   -r requirements.txt
    #   trio
sortedcontainers==2.4.0
    # via
    #   -r requirements.txt
    #   trio
sqlalchemy==1.4.54
    # via
    #   -r requirements.txt
    #   alembic
    #   apache-superset
    #   flask-appbuilder
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   shillelagh
    #   sqlalchemy-utils
sqlalchemy-utils==0.38.3
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
sqlglot==25.34.1
    # via
    #   -r requirements.txt
    #   apache-superset
sqlparse==0.5.3
    # via
    #   -r requirements.txt
    #   apache-superset
sshtunnel==0.4.0
    # via
    #   -r requirements.txt
    #   apache-superset
tabulate==0.8.10
    # via
    #   -r requirements.txt
    #   apache-superset
trio==0.29.0
    # via
    #   -r requirements.txt
    #   selenium
    #   trio-websocket
trio-websocket==0.12.2
    # via
    #   -r requirements.txt
    #   selenium
typing-extensions==4.12.2
    # via
    #   -r requirements.txt
    #   alembic
    #   apache-superset
    #   flask-limiter
    #   limits
    #   pyopenssl
    #   referencing
    #   shillelagh
tzdata==2025.1
    # via
    #   -r requirements.txt
    #   celery
    #   kombu
    #   pandas
url-normalize==1.4.3
    # via
    #   -r requirements.txt
    #   requests-cache
urllib3==2.3.0
    # via
    #   -r requirements.txt
    #   requests
    #   requests-cache
    #   selenium
vine==5.1.0
    # via
    #   -r requirements.txt
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via
    #   -r requirements.txt
    #   prompt-toolkit
werkzeug==3.1.3
    # via
    #   -r requirements.txt
    #   flask
    #   flask-appbuilder
    #   flask-jwt-extended
    #   flask-login
wrapt==1.17.2
    # via
    #   -r requirements.txt
    #   deprecated
wsproto==1.2.0
    # via
    #   -r requirements.txt
    #   trio-websocket
wtforms==3.2.1
    # via
    #   -r requirements.txt
    #   apache-superset
    #   flask-appbuilder
    #   flask-wtf
    #   wtforms-json
wtforms-json==0.3.5
    # via
    #   -r requirements.txt
    #   apache-superset
xlsxwriter==3.0.9
    # via
    #   -r requirements.txt
    #   apache-superset
zipp==3.21.0
    # via
    #   -r requirements.txt
    #   importlib-metadata
zstandard==0.23.0
    # via
    #   -r requirements.txt
    #   flask-compress
