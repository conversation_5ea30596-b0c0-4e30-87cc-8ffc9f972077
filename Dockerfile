FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    SUPERSET_HOME=/app \
    FLASK_ENV=production \
    SUPERSET_ENV=production \
    SUPERSET_CONFIG_PATH=/app/superset_config.py

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        default-libmysqlclient-dev \
        libpq-dev \
        libsasl2-dev \
        libldap2-dev \
        libssl-dev \
        libffi-dev \
        curl \
        netcat-traditional \
        git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt /app/requirements-prod.txt
# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r /app/requirements-prod.txt && \
    pip install --no-cache-dir apache-superset

# Copy configuration and custom files
COPY superset_config.py /app/superset_config.py
COPY ./custom_static /app/custom_static/
COPY ./templates /app/templates/
COPY ./scripts /app/scripts/
COPY ./exports /app/exports/
COPY ./imports /app/imports/

# Make the export-import script executable
RUN chmod +x /app/scripts/export-import.sh

# Create directories for exports and backups
RUN mkdir -p /app/exports /app/backups

# Create a startup script that sets up network to host machine
RUN echo '#!/bin/bash\n\
# Get host IP - different approach depending on OS\n\
if [ -n "$HOST_IP" ]; then\n\
    echo "Using provided HOST_IP: $HOST_IP"\n\
elif [ "$(uname)" == "Darwin" ]; then\n\
    # macOS uses host.docker.internal\n\
    export HOST_IP="host.docker.internal"\n\
    echo "Using macOS host: $HOST_IP"\n\
else\n\
    # Linux needs to get the Docker host IP\n\
    export HOST_IP=$(ip route | grep default | awk "{print \\$3}")\n\
    echo "Using Linux host: $HOST_IP"\n\
fi\n\
\n\
# Set Cube.js connection URL to host machine\n\
export CUBEJS_API_URL="http://$HOST_IP:4000/cubejs-api/v1"\n\
echo "Connecting to CubeJS at: $CUBEJS_API_URL"\n\
\n\
# If this is the first run, initialize the database\n\
if [ "$INIT_DB" = "true" ]; then\n\
    echo "Initializing Superset database..."\n\
    superset db upgrade\n\
    superset fab create-admin \\\n\
        --username "${ADMIN_USERNAME:-admin}" \\\n\
        --firstname "${ADMIN_FIRSTNAME:-Admin}" \\\n\
        --lastname "${ADMIN_LASTNAME:-Admin}" \\\n\
        --email "${ADMIN_EMAIL:-<EMAIL>}" \\\n\
        --password "${ADMIN_PASSWORD:-admin}"\n\
    superset init\n\
fi\n\
\n\
# Start Superset\n\
echo "Starting Superset..."\n\
gunicorn \\\n\
    --bind=0.0.0.0:8088 \\\n\
    --workers=4 \\\n\
    --timeout=120 \\\n\
    --worker-class=gevent \\\n\
    --limit-request-line=0 \\\n\
    --limit-request-field_size=0 \\\n\
    "superset.app:create_app()"\n\
' > /app/docker-entrypoint.sh && chmod +x /app/docker-entrypoint.sh

# Expose the default port
EXPOSE 8088

# Use a non-root user for security
RUN useradd -ms /bin/bash superset
RUN chown -R superset:superset /app
USER superset

# Set entrypoint
ENTRYPOINT ["/app/docker-entrypoint.sh"]
