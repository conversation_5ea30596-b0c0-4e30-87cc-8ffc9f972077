/**
 * Mobile Enhancement Scripts for Ajna Frontend
 * Provides mobile-specific optimizations and touch interactions
 */

(function() {
    'use strict';
    
    // Mobile detection
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    
    // Touch event handling
    let touchStartY = 0;
    let touchStartX = 0;
    
    /**
     * Initialize mobile enhancements
     */
    function initMobileEnhancements() {
        if (!isMobile) return;
        
        // Add mobile class to body
        document.body.classList.add('mobile-device');
        
        if (isIOS) {
            document.body.classList.add('ios-device');
            handleIOSSpecificOptimizations();
        }
        
        if (isAndroid) {
            document.body.classList.add('android-device');
            handleAndroidSpecificOptimizations();
        }
        
        // Initialize touch enhancements
        initTouchEnhancements();
        
        // Initialize form enhancements
        initFormEnhancements();
        
        // Initialize viewport optimizations
        initViewportOptimizations();
        
        // Initialize accessibility enhancements
        initAccessibilityEnhancements();
    }
    
    /**
     * Handle iOS-specific optimizations
     */
    function handleIOSSpecificOptimizations() {
        // Prevent zoom on input focus
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                // Temporarily disable zoom
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            });
            
            input.addEventListener('blur', function() {
                // Re-enable zoom after a delay
                setTimeout(() => {
                    const viewport = document.querySelector('meta[name="viewport"]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes');
                    }
                }, 100);
            });
        });
        
        // Handle safe area insets
        if (CSS.supports('padding-top: env(safe-area-inset-top)')) {
            document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
            document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
        }
    }
    
    /**
     * Handle Android-specific optimizations
     */
    function handleAndroidSpecificOptimizations() {
        // Handle keyboard visibility
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            if (heightDifference > 150) {
                // Keyboard is likely open
                document.body.classList.add('keyboard-open');
            } else {
                // Keyboard is likely closed
                document.body.classList.remove('keyboard-open');
            }
        });
    }
    
    /**
     * Initialize touch enhancements
     */
    function initTouchEnhancements() {
        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button, .btn, a[role="button"]');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function(e) {
                this.classList.add('touch-active');
                touchStartY = e.touches[0].clientY;
                touchStartX = e.touches[0].clientX;
            }, {passive: true});
            
            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            }, {passive: true});
            
            button.addEventListener('touchcancel', function() {
                this.classList.remove('touch-active');
            }, {passive: true});
        });
        
        // Improve scroll performance
        const scrollElements = document.querySelectorAll('.scrollable, .modal-body');
        scrollElements.forEach(element => {
            element.style.webkitOverflowScrolling = 'touch';
        });
    }
    
    /**
     * Initialize form enhancements
     */
    function initFormEnhancements() {
        // Auto-focus first input on mobile (with delay to prevent keyboard flash)
        const firstInput = document.querySelector('input[type="text"], input[type="email"], input[type="password"]');
        if (firstInput && !isMobile) {
            setTimeout(() => {
                firstInput.focus();
            }, 300);
        }
        
        // Add loading states to form submissions
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.classList.add('loading');
                    
                    // Add spinner if not already present
                    if (!submitButton.querySelector('.spinner')) {
                        const spinner = document.createElement('span');
                        spinner.className = 'spinner';
                        submitButton.insertBefore(spinner, submitButton.firstChild);
                    }
                }
            });
        });
        
        // Password visibility toggle
        const passwordToggles = document.querySelectorAll('.password-toggle-icon');
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const passwordInput = this.parentElement.querySelector('input[type="password"], input[type="text"]');
                if (passwordInput) {
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        this.classList.add('password-visible');
                    } else {
                        passwordInput.type = 'password';
                        this.classList.remove('password-visible');
                    }
                }
            });
        });
    }
    
    /**
     * Initialize viewport optimizations
     */
    function initViewportOptimizations() {
        // Handle orientation changes
        window.addEventListener('orientationchange', function() {
            // Force viewport recalculation
            setTimeout(() => {
                window.scrollTo(0, 0);
                
                // Trigger resize event
                window.dispatchEvent(new Event('resize'));
            }, 100);
        });
        
        // Handle dynamic viewport height for mobile browsers
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 100);
        });
    }
    
    /**
     * Initialize accessibility enhancements
     */
    function initAccessibilityEnhancements() {
        // Add focus indicators for keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Improve touch target sizes
        const smallTargets = document.querySelectorAll('a, button, input, select, textarea');
        smallTargets.forEach(target => {
            const rect = target.getBoundingClientRect();
            if (rect.width < 44 || rect.height < 44) {
                target.style.minWidth = '44px';
                target.style.minHeight = '44px';
            }
        });
    }
    
    /**
     * Handle network status
     */
    function initNetworkHandling() {
        if ('navigator' in window && 'onLine' in navigator) {
            function updateNetworkStatus() {
                if (navigator.onLine) {
                    document.body.classList.remove('offline');
                    document.body.classList.add('online');
                } else {
                    document.body.classList.remove('online');
                    document.body.classList.add('offline');
                    
                    // Show offline message
                    showOfflineMessage();
                }
            }
            
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
            updateNetworkStatus();
        }
    }
    
    /**
     * Show offline message
     */
    function showOfflineMessage() {
        const existingMessage = document.querySelector('.offline-message');
        if (existingMessage) return;
        
        const message = document.createElement('div');
        message.className = 'offline-message';
        message.innerHTML = `
            <div class="offline-content">
                <i class="fas fa-wifi-slash"></i>
                <span>You're offline. Please check your connection.</span>
            </div>
        `;
        
        document.body.appendChild(message);
        
        // Auto-hide when back online
        const hideOnOnline = () => {
            if (navigator.onLine) {
                message.remove();
                window.removeEventListener('online', hideOnOnline);
            }
        };
        
        window.addEventListener('online', hideOnOnline);
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileEnhancements);
    } else {
        initMobileEnhancements();
    }
    
    // Initialize network handling
    initNetworkHandling();
    
})();
