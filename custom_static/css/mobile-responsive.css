/* Mobile-First Responsive Design for Ajna Frontend */

/* Base styles - Mobile first approach */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-optimized login container - High specificity */
.lg-container,
div.lg-container {
  min-height: 100vh !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: column !important;
  padding: 16px !important;
  position: relative !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

/* Modern, professional login card - High specificity */
.lg-card,
div.lg-card {
  border: 1px solid #e5e7eb !important;
  background: white !important;
  width: 100% !important;
  max-width: 400px !important;
  padding: 32px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 16px 0 !important;
}

/* Logo optimization - smaller, more refined - High specificity */
.lg-logo,
div.lg-logo {
  background-color: #1a1a1a !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-bottom: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.lg-logo svg,
div.lg-logo svg {
  width: 24px !important;
  height: 24px !important;
}

/* Modern typography */
.lg-card h3 {
  font-weight: 600;
  margin-bottom: 6px;
  font-size: 1.5rem;
  text-align: center;
  color: #111827;
  letter-spacing: -0.025em;
}

.lg-card p {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 32px;
  text-align: center;
  line-height: 1.5;
  font-weight: 400;
}

/* Modern form styling */
.lg-form {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.lg-form-field {
  margin-bottom: 20px;
}

.lg-form-label {
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 0.875rem;
  color: #374151;
  display: block;
  letter-spacing: 0.025em;
}

/* Modern, professional input fields */
.lg-form-input {
  color: #1f2937;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 15px; /* Prevents zoom on iOS */
  border: 1px solid #d1d5db;
  width: 100%;
  transition: all 0.2s ease;
  background-color: #ffffff;
  -webkit-appearance: none;
  appearance: none;
  font-weight: 400;
  line-height: 1.5;
}

.lg-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #ffffff;
}

.lg-form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.lg-form-input:hover:not(:focus) {
  border-color: #9ca3af;
}

/* Modern, professional button */
.lg-button {
  border-radius: 6px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;
  border: none;
  margin-top: 16px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly minimum */
  width: 100%;
  letter-spacing: 0.025em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.lg-button:hover {
  background: #2563eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.lg-button:active {
  transform: translateY(0);
  background: #1d4ed8;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Modern OAuth buttons */
.btn.btn-outline-secondary {
  border-radius: 6px !important;
  padding: 11px 16px !important;
  font-size: 14px !important;
  border: 1px solid #d1d5db !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  text-decoration: none !important;
  color: #374151 !important;
  background: white !important;
  transition: all 0.2s ease !important;
  min-height: 44px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.btn.btn-outline-secondary:hover {
  border-color: #9ca3af !important;
  background: #f9fafb !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  color: #1f2937 !important;
}

.btn.btn-outline-secondary:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* Divider styling */
.lg-continue {
  margin: 20px 0 16px 0;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0 12px;
  white-space: nowrap;
}

/* Footer optimization */
.text-muted.medium {
  font-size: 0.8rem !important;
  color: #9ca3af !important;
  text-align: center;
  margin-top: 24px !important;
}

/* Password field styling */
.password-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.password-input-container {
  position: relative;
}

.password-toggle-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle-icon:hover {
  background-color: #f3f4f6;
}

/* Forgot password link */
.forgot-password-link {
  color: #2563eb !important;
  text-decoration: none !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  transition: color 0.2s ease !important;
}

.forgot-password-link:hover {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

/* Divider section */
.divider-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0 16px 0;
}

.divider-line {
  flex-grow: 1;
  height: 1px;
  background-color: #e5e7eb;
}

/* OAuth providers section */
.oauth-providers-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.oauth-provider-button {
  width: 100%;
}

.oauth-btn {
  width: 100% !important;
}

/* Sign up section */
.signup-section {
  text-align: center;
  margin-top: 20px;
}

.signup-text {
  font-size: 16px;
  margin: 8px 0 0 0;
  color: #6b7280;
}

.signup-link {
  color: #2563eb !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  transition: color 0.2s ease !important;
}

.signup-link:hover {
  color: #1d4ed8 !important;
  text-decoration: underline !important;
}

/* Footer section */
.footer-section {
  text-align: center;
  margin-top: 24px;
}

/* Responsive breakpoints */

/* Small mobile devices (320px and up) */
@media (max-width: 374px) {
  .lg-card {
    padding: 16px;
    margin: 8px 0;
  }
  
  .lg-card h3 {
    font-size: 1.3rem;
  }
  
  .lg-card p {
    font-size: 0.9rem;
  }
  
  .lg-container {
    padding: 12px;
  }
}

/* Medium mobile devices (375px and up) */
@media (min-width: 375px) {
  .lg-card {
    padding: 32px;
  }

  .lg-logo {
    width: 52px;
    height: 52px;
  }

  .lg-logo svg {
    width: 26px;
    height: 26px;
  }
}

/* Large mobile devices (414px and up) */
@media (min-width: 414px) {
  .lg-card {
    max-width: 380px;
    padding: 28px;
  }
}

/* Tablet devices (768px and up) */
@media (min-width: 768px) {
  .lg-container {
    padding: 32px;
  }

  .lg-card {
    max-width: 420px;
    padding: 32px;
  }

  .lg-card h3 {
    font-size: 1.75rem;
  }

  .lg-card p {
    font-size: 1rem;
  }

  .lg-logo {
    width: 56px;
    height: 56px;
  }

  .lg-logo svg {
    width: 28px;
    height: 28px;
  }

  /* On tablets, show OAuth buttons side by side */
  .oauth-providers-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .oauth-provider-button {
    width: 100%;
  }
}

/* Desktop devices (1024px and up) */
@media (min-width: 1024px) {
  .lg-container {
    padding: 40px;
    min-height: 100vh;
  }

  .lg-card {
    max-width: 480px;
    padding: 40px;
  }

  .lg-card h3 {
    font-size: 2rem;
    margin-bottom: 12px;
  }

  .lg-card p {
    font-size: 1.1rem;
    margin-bottom: 32px;
  }

  .lg-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .lg-logo svg {
    width: 50px;
    height: 50px;
  }

  /* Desktop OAuth button layout */
  .oauth-providers-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  /* Better spacing for desktop */
  .lg-form-field {
    margin-bottom: 20px;
  }

  .lg-button {
    padding: 16px 24px;
    font-size: 18px;
    margin-top: 16px;
  }

  .divider-section {
    margin: 32px 0 24px 0;
  }

  .signup-section {
    margin-top: 32px;
  }

  .footer-section {
    margin-top: 40px;
  }
}

/* Large desktop devices (1440px and up) */
@media (min-width: 1440px) {
  .lg-card {
    max-width: 520px;
    padding: 48px;
  }

  .lg-card h3 {
    font-size: 2.25rem;
  }

  .lg-card p {
    font-size: 1.2rem;
  }

  .lg-logo {
    width: 90px;
    height: 90px;
  }

  .lg-logo svg {
    width: 55px;
    height: 55px;
  }
}

/* Hide header on login page */
header {
  display: none !important;
}

/* Ensure proper touch targets on mobile only */
@media (max-width: 1023px) {
  button, a, input[type="submit"], input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Improve accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Touch states and mobile enhancements */
.touch-active {
  transform: scale(0.98) !important;
  opacity: 0.8 !important;
  transition: all 0.1s ease !important;
}

/* Loading states */
.loading {
  position: relative;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* Keyboard navigation support */
.keyboard-navigation *:focus {
  outline: 2px solid #2563eb !important;
  outline-offset: 2px !important;
}

/* Network status indicators */
.offline-message {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ef4444;
  color: white;
  padding: 12px;
  text-align: center;
  z-index: 9999;
  font-size: 14px;
  font-weight: 500;
}

.offline-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* iOS specific styles */
.ios-device .lg-container {
  padding-top: max(16px, env(safe-area-inset-top));
  padding-bottom: max(16px, env(safe-area-inset-bottom));
}

/* Android keyboard handling */
.android-device.keyboard-open .lg-container {
  height: auto;
  min-height: auto;
}

/* Password visibility toggle */
.password-toggle-icon.password-visible svg path {
  fill: #2563eb;
}

/* Improved focus states for mobile */
.lg-form-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: none; /* Prevent zoom on iOS */
}

/* Better touch targets for mobile */
.mobile-device button,
.mobile-device a,
.mobile-device input[type="submit"],
.mobile-device input[type="button"] {
  min-height: 44px;
  min-width: 44px;
}

/* Desktop-specific optimizations */
.desktop-device .lg-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.desktop-device .oauth-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Remove touch effects on desktop */
.desktop-device .touch-active {
  transform: none !important;
  opacity: 1 !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent text selection on UI elements */
.lg-button,
.btn,
.oauth-btn {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .lg-container {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .lg-card {
    background: #374151;
    color: #f9fafb;
  }

  .lg-card h3 {
    color: #f9fafb;
  }

  .lg-form-input {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }

  .lg-form-input::placeholder {
    color: #9ca3af;
  }

  .lg-form-label {
    color: #e5e7eb;
  }

  .forgot-password-link,
  .signup-link {
    color: #60a5fa !important;
  }

  .forgot-password-link:hover,
  .signup-link:hover {
    color: #3b82f6 !important;
  }
}
